<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:fn="http://xmlns.jcp.org/jsp/jstl/functions"
	xmlns:c="http://xmlns.jcp.org/jsp/jstl/core">

<h:head>
	<f:facet name="first">
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta http-equiv="Content-Type"
			content="text/html; charset=ISO-8859-1" />
		<meta name="viewport"
			content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
		<meta name="apple-mobile-web-app-capable" content="yes" />
	</f:facet>

	<h:outputScript library="sentinel-layout" name="js/layout.js" />
	<h:outputScript name="locale-primefaces.js" library="js" />
	<h:outputScript name="css_browser_selector.js" library="js" />

	<h:outputStylesheet library="sentinel-layout"
		name="css/font-icon-layout.css" />
	<h:outputStylesheet library="sentinel-layout"
		name="css/sentinel-layout.css" />
	<h:outputStylesheet library="sentinel-layout"
		name="css/core-layout.css" />

	<h:outputStylesheet library="css" name="index.css" />

	<title>SICAP Análise | Sistema de Análise de Atos de Pessoal</title>

	<link rel="icon"
		href="#{request.contextPath}/resources/imagens/logo_sicap.png" />

	<ui:insert name="scripts">

		<script type="text/javascript">
			var tempoSessao = 7200;
			var timer = setInterval(function() {
				if( tempoSessao === 0 ) {
					document.getElementById('session-countdown').innerHTML = "00min:00s";
		    		clearInterval( timer );			    	
		  		}
				var segundos = tempoSessao % 60;
				var minutos = parseInt(tempoSessao / 60);
				segundos = (segundos + "").replace(/^(\d)$/, '0$1');
				minutos = (minutos + "").replace(/^(\d)$/, '0$1');
		   		document.getElementById('session-countdown').innerHTML = minutos + "min:" + segundos + "s";   
		   		tempoSessao--;
			}, 1000);
								
				$(document).ready(function(){
					ajustaHeights();
					$('#sm-topmenu li .ui-commandlink').removeClass('ui-commandlink');
		
					if ($(document).width() >= 641) {
						$('#layout-menubar').addClass('slimmenu');
					}
					if ($(document).width() >= 1200) {
						$('#layout-menubar').removeClass('slimmenu');
					}
					
					$(window).on('resize', function(){
						ajustaHeights();
						
						if ($(document).width() >= 641) {
							$('#layout-menubar').addClass('slimmenu');
						}
						if ($(document).width() >= 1200) {
							$('#layout-menubar').removeClass('slimmenu');
						}
						
					}).resize();
				});
				
				$(document).ajaxSuccess(function() {
					ajustaHeights();
				});
				
				ajustaHeights = function () {
 					$('#layout-menubar').css('min-height', 100);
 					$('#layout-menubar').css('height', 100);
 					$('#corpo').css('min-height', 100);
 					$('#corpo').css('min-height', $(document).height() - $('#layout-header').height() - $('#footer').height() - 10);
 					$('#layout-menubar').css('height', $('#corpo').height() + $('#footer').height() + 5);
				};

	
			</script>
	</ui:insert>

	<style type="text/css">
#overlay {
	visibility: hidden;
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	text-align: center;
	z-index: 200;
	background-color: black;
	opacity: 0.9;
}

#overlay div {
	width: 300px;
	margin: 100px auto; G3 SUPERIOR N5 background-color : #fff;
	border: 1px solid #000;
	padding: 15px;
	text-align: center;
}
</style>

	<ui:insert name="head" />

</h:head>

<h:body>


	<div id="layout-header" class="Unselectable fontRegular">
		<h:form prependId="false">
			<h:outputLink id="tceLink" value="http://www.tce.ac.gov.br"
				title="Tribunal de Contas do Estado do Acre">
				<h:graphicImage id="iconTce" library="imagens" name="icon_tce.png" styleClass="ShowOnDesktop"/>
				<h:graphicImage id="iconTceMobile" library="imagens" name="icon_tce3.png" styleClass="ShowOnMobile"/>
			</h:outputLink>

			<p:commandLink id="sicapLink" title="Página inicial"
				action="/principal.xhtml?faces-redirect=true">
				<h:graphicImage id="iconSistema" library="imagens"
					name="logo_sicap.png" />
				<h:outputText id="lblHeader" value="SICAP Análise" />
			</p:commandLink>

			<ul id="sm-topmenu" class="layout-header-widgets white Fs14">
				<li class="Fleft BordRadHalf TexAlCenter"><a
					class="white Unselectable" onclick="PF('dialogoSelecao').show();"
					title="Gerenciar outra entidade"> <i class="icon-th-list-2"></i>
				</a></li>
				<li class="Fleft BordRadHalf TexAlCenter"><p:commandLink
						id="logout" title="Sair do Sistema" action="#{loginBean.logoff()}"
						styleClass="white Unselectable">
						<i class="icon-power white"></i>
					</p:commandLink></li>
			</ul>

			<!-- NEVER REMOVE FOLLOWING 'UL' TAG ! BECAUSE THIS CONTAINS RESPONSIVE MODE HEADER MENU OPEN-CLOSE BUTTON -->
			<ul id="sm-mobiletopmenu" class="layout-header-widgets white Fs14">
				<li
					class="Fleft BordRadHalf TexAlCenter Animated05 DisplayOnResponsive"><i
					class="icon-th-1"></i></li>
			</ul>
			<!-- ****** -->
			<ui:insert name="header-widgets" />
		</h:form>
	</div>

	<!-- 	<ui:insert name="menubar"> -->
	<!-- 		<h:panelGroup rendered="#{loginBean.isADM()}"> -->
	<div id="layout-menubar" class="fontRegular">
		<div class="layout-menubarinner-box" id="buttonArea">
			<a href="#" id="layout-menubar-resize" class="BordRad3"
				title="Menu Resize"> <i class="icon-th-list-2"></i>
			</a> <a href="#" id="layout-menubar-resize2" class="BordRad3"
				title="Open Menu"> <i class="icon-menu"></i>
			</a>
		</div>

		<!-- 			<h:form id="menuForm"> -->

		<ul class="layout-menubar-container">
			<li><a
				href="#{request.contextPath}/principal.xhtml?faces-redirect=true"><i
					class="icon-home yellow i"></i> Página Inicial </a></li>
			<li><a id="idlinkda" href="#"
				onclick="Sentinel.openSubMenu(this);"> <i
					class="icon-eye yellow i"></i> Auditoria <i
					class="icon-angle-down Fright yellow"></i>
			</a>
				<ul class="layout-menubar-submenu-container level_1">
					<li><a class="marginLevel-1"
						href="#{request.contextPath}/auditoria/acumulacao/index.xhtml">
							<i class="icon-users yellow"></i> Servidores Acumulando Cargos
					</a></li>
					<li><a
						href="#{request.contextPath}/auditoria/acumulacao/entidades.xhtml"
						class="marginLevel-1"> <i class="icon-bank yellow"></i>
							Acumulações por Entidade
					</a></li>


					<c:if test="#{loginBean.getPermissao('SICAP_ADMINISTRADOR') or loginBean.perfilAuditoria}">
						<li><a
							href="#{request.contextPath}/auditoria/analise/index.xhtml"
							class="marginLevel-1"> <i class="icon-paste yellow"></i>
								Gerenciar Acumulações
						</a></li>

						<li><a
							href="#{request.contextPath}/auditoria/analise/notificacoes.xhtml"
							class="marginLevel-1"> <i class="icon-mail yellow"></i>
								Notificações Emitidas
						</a></li>

<!-- 						<li><a -->
<!-- 							href="#{request.contextPath}/auditoria/distribuicao/index.xhtml" -->
<!-- 							class="marginLevel-1"> <i class="icon-tasks yellow"></i> -->
<!-- 								Minhas Tarefas -->
<!-- 						</a></li> -->

						<li><a
							href="#{request.contextPath}/auditoria/grupo/index.xhtml"
							class="marginLevel-1"> <i class="icon-users yellow"></i>
								Grupos de Trabalho
						</a></li>
					</c:if>
				</ul></li>

			<li><a id="idlinkde" href="#"
				onclick="Sentinel.openSubMenu(this);"> <i
					class="icon-bank yellow i"></i> Dados Por Entidade<i
					class="icon-angle-down Fright yellow"></i>
			</a>
				<ul class="layout-menubar-submenu-container level_1">
					<li><a
						href="#{request.contextPath}/dados/entidade/cargo/cargos.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-briefcase yellow"></i>
							Cargos
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/servidor/servidores.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-user yellow"></i>
							Servidores
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/pensionista/pensionistas.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-users yellow"></i>
							Pensionistas
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/verba/verbas.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-list-alt yellow"></i>
							Verbas
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/tipoFolha/tipoFolha.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-docs yellow"></i> Tipos
							de Folha
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/tabelaVencimentos/tabelaVencimentos.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-table yellow"></i>
							Tabelas de Vencimentos
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/unidadeLotacao/unidadeLotacao.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-sitemap yellow"></i>
							Unidades de Lotação
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/folha/resumoFolha.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-calculator yellow"></i>
							Resumo da Folha
					</a></li>
					<li><a
						href="#{request.contextPath}/dados/entidade/folha/resumoEntidade.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-cubes yellow"></i>
							Resumo da Entidade
					</a></li>
				</ul></li>
			<li><a id="idlinkdg" href="#"
				onclick="Sentinel.openSubMenu(this);"> <i
					class="icon-info-circled yellow i"></i> Dados Gerais <i
					class="icon-angle-down Fright yellow"></i>
			</a>
				<ul class="layout-menubar-submenu-container level_1">
					<li><a id="idlinkcp"
						href="#{request.contextPath}/dados/gerais/consultaBeneficiario/consultaBeneficiario.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-user yellow"></i>
							Consultar Pessoa
					</a></li>
					<li><a 
						href="#{request.contextPath}/dados/gerais/consultaCargo/consultaCargo.xhtml?faces-redirect=true"
						class="marginLevel-1"> <i class="icon-bank yellow"></i>
							Consultar Cargos por Entidade
					</a></li>
				</ul></li>
			<li><a id="idLinkpi"
				href="#{request.contextPath}/exportar/exportarDados.xhtml?faces-redirect=true"
				onclick="Sentinel.toggleSubMenu(this);"><i
					class="icon-database yellow i"></i> Exportar Dados </a></li>


		</ul>
		<!-- 			</h:form> -->
	</div>

	<!-- 	</ui:insert> -->

	<div id="layout-portlets-cover" class="fontRegular">
		<div id="corpo" class="Container100 BorBotLeaden ui-fluid corpo">
			<!-- 			<div class="Container PadWithBorder5"> -->
			<!-- 				<div class="Container60 Responsive50 "> -->
			<!-- 					<h:outputText value="Entidade: " styleClass="hardblue statusTop" /> -->
			<!-- 					<h:outputText value="#{loginBean.entidade.nome}" /> -->
			<!-- 				</div> -->
			<!-- 				<div class="Container40 Responsive50"> -->
			<!-- 					<h:panelGrid columns="2" styleClass="Fright Fs12" -->
			<!-- 						columnClasses="TexAlRight,TexAlLeft"> -->
			<!-- 						<h:outputText value="Usuário: " styleClass="hardblue statusTop" /> -->
			<!-- 						<h:outputText value="#{loginBean.usuario.nome}" /> -->
			<!-- 						<h:outputText value="Sua sessão expira em: " -->
			<!-- 							styleClass="hardblue statusTop" /> -->
			<!-- 						<span id="session-countdown" class="statusTopTextLabel"></span> -->
			<!-- 					</h:panelGrid> -->

			<!-- 				</div> -->
			<!-- 			</div> -->


			<div id="cabecalho">
				<div id="entidade">
					<h:outputText value="Entidade: " styleClass="hardblue statusTop" />
					<h:outputText value="#{loginBean.entidade.nome}" />
				</div>
				<div id="timer">
					<h:panelGroup styleClass="TexAlRight">
						<h:outputText value="Sua sessão expira em: "
							styleClass="hardblue statusTop" />
						<span id="session-countdown" class="statusTopTextLabel"></span>
						<br />

						<h:outputText value="Usuário: " styleClass="hardblue statusTop" />
						<h:outputText value="#{loginBean.usuario.nome}" />
					</h:panelGroup>
				</div>
				<h:form prependId="false">
					<p:idleMonitor timeout="7200000" onidle="PF('idleDialog').show()" />
					<p:dialog id="idleDialog" widgetVar="idleDialog" resizable="false"
						closable="false" modal="true" width="400" height="150"
						header="Sessão Expirada">
						<h:panelGrid columns="1" styleClass="TexAlCenter">
							<h:outputText
								value="A página ficou inativa por muito tempo e, por questões de segurança, você foi desconectado."
								styleClass="Fs14" />
							<br />
							<p:commandButton value="Clique Aqui"
								action="login.xhtml?faces-redirect=true" ajax="false"
								immediate="true" />
						</h:panelGrid>
					</p:dialog>
				</h:form>
			</div>
			<div class="EmptyBox10" />
			<div id="mainContent">
				<h:form prependId="false">
					<p:dialog id="dialogoSelecao" header="Selecionar Entidade"
						widgetVar="dialogoSelecao" closable="true" resizable="false" 
						modal="true">

						<div class="Container100">
							<div class="ContainerIndent">
								<p:dataTable paginator="true" paginatorPosition="bottom"
									emptyMessage="Nenhuma entidade encontrada."
									value="#{loginBean.listaEntidades}" var="entidade" rows="10"
									selectionMode="single" selection="#{loginBean.entidade}"
									rowKey="#{entidade.idEntidadeCjur}">
									<p:ajax event="rowSelect"
										listener="#{loginBean.selecaoEntidade()}" />

									<f:facet name="header">
										<h:outputText value="Selecione uma entidade para gerenciar" />
									</f:facet>

									<p:column headerText="Nome" filterBy="#{entidade.nome}"
										filterMatchMode="contains" filterStyle="width: 100% !important;">
										<h:outputText value="#{entidade.nome}" />
									</p:column>

									<p:column headerText="Ente" filterBy="#{entidade.ente}"
										filterMatchMode="contains" filterStyle="width: 100% !important;">
										<h:outputText value="#{entidade.ente}" />
									</p:column>

									<p:column headerText="Poder">
										<h:outputText value="#{entidade.poder}" />
									</p:column>

									<p:column headerText="Classificacao Administrativa">
										<h:outputText value="#{entidade.classificacaoAdministrativa}" />
									</p:column>
								</p:dataTable>
							</div>
						</div>
					</p:dialog>

					<p:ajaxStatus onstart="PF('statusAjaxDialog').show();"
						onsuccess="PF('statusAjaxDialog').hide();" />

					<p:dialog modal="true" widgetVar="statusAjaxDialog"
						header="Carregando..." draggable="false" closable="false"
						resizable="false">
						<p:graphicImage library="imagens" name="ajaxloadingbar.gif" />
					</p:dialog>
				</h:form>

				<ui:insert name="content" />
			</div>

			<div id="footer" class="Container100">
				<p:separator styleClass="hardblue" />
				<div class="ContainerIndent">
					<div class="Container100">
						<a href="http://tceac.tc.br" target="_blank"
							class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft ">SITE
							DO TCE</a> <span class="Fleft gray Fs12" style="padding: 0px 5px;">|</span>
						<a href="http://sistemas.tceac.tc.br/portaldogestor" target="_blank"
							class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft ">PORTAL
							DO GESTOR</a> <span class="Fleft gray Fs12" style="padding: 0px 5px;">|</span>
						<a href="http://sistemas.tceac.tc.br/cidadao" target="_blank"
							class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft ">PORTAL
							DO CIDADÃO</a> <span class="Fleft gray Fs12" style="padding: 0px 5px;">|</span>
						<a href="http://sistemas.tceac.tc.br/portaldaslicitacoes"
							target="_blank"
							class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft ">PORTAL
							DAS LICITAÇÕES</a> <span class="Fleft gray Fs12"
							style="padding: 0px 5px;">|</span> <a
							href="http://sistemas.tceac.tc.br/transparencia" target="_blank"
							class="DispInlBlock FontSourceSansLight hardblue Fs12 Fleft ">TRANSPARÊNCIA</a>
		
						<div class="EmptyBox10 ShowOnMobile"></div>
						<span
							class="DispInlBlock FontSourceSansLight gray Fs12 Fright  TexAlRight FloatNoneOnMobile">
							TCE-AC | Todos os direitos reservados.</span>
					</div>

					<div class="EmptyBox10"></div>

					<div class="Container50 Responsive100">
						<div class="EmptyBox10"></div>
						<span class="DispBlock Wid100 FontSourceSansLight gray Fs12 ">Tribunal
							de Contas do Estado do Acre <br /> Av. Ceará, 2994, 7° BEC - Rio
							Branco-Acre - CEP 69.918-111 <br /> Suporte:
							<EMAIL> | Tel.:(68) 3025-2009
						</span>
					</div>
					<div class="Container50 Responsive100">
						<div class="DispInlBlock Fright">
							<a href="https://www.facebook.com/tceac" target="_blank"
								class="hardblue Fs30"><i class="fa fa-facebook-square"
								title="Facebook"></i></a> <a
								href="https://plus.google.com/105996813613508600853"
								target="_blank" class="hardblue Fs30"><i
								class="fa fa-google-plus-square" title="Google+"></i></a> <a
								href="https://www.youtube.com/channel/UCoNe1qAoUfR6FuQ1yOsbxjQ"
								target="_blank" class="hardblue Fs30"><i
								class="fa fa-youtube-square" title="YouTube"></i></a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</h:body>
</html>