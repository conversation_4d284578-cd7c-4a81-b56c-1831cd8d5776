<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://xmlns.jcp.org/xml/ns/javaee"
	xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
	id="WebApp_ID" version="4.0">
	<display-name>sicapAnaliseV2</display-name>
	<welcome-file-list>
		<welcome-file>principal.xhtml</welcome-file>
	</welcome-file-list>
	<context-param>
		<param-name>primefaces.UPLOADER</param-name>
		<param-value>commons</param-value>
	</context-param>
	<filter>
		<filter-name>PrimeFaces FileUpload Filter</filter-name>
		<filter-class>org.primefaces.webapp.filter.FileUploadFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>PrimeFaces FileUpload Filter</filter-name>
		<servlet-name>Faces Servlet</servlet-name>
	</filter-mapping>
	<session-config>
		<session-timeout>120</session-timeout>
	</session-config>
	<security-role>
		<role-name>*</role-name>
	</security-role>
	<security-role>
		<role-name>SICAP_ADMINISTRADOR</role-name>
	</security-role>
	<security-role>
		<role-name>SICAP_AUDITORES</role-name>
	</security-role>
	<security-role>
		<role-name>SICAP_INSPETORES</role-name>
	</security-role>
	<login-config>
		<auth-method>FORM</auth-method>
		<realm-name>ldap_security_domain</realm-name>
		<form-login-config>
			<form-login-page>/login.xhtml?faces-redirect=true</form-login-page>
			<form-error-page>/login.xhtml?faces-redirect=true</form-error-page>
		</form-login-config>
	</login-config>
	<security-constraint>
		<web-resource-collection>
			<web-resource-name>Acesso restrito</web-resource-name>
			<url-pattern>/auditoria/acumulacao/*</url-pattern>
			<url-pattern>/dados/*</url-pattern>
			<url-pattern>/exportar/*</url-pattern>
			<url-pattern>/gerenciamento/*</url-pattern>
			<url-pattern>/relatorios/*</url-pattern>
			<url-pattern>/principal.xhtml</url-pattern>
			<url-pattern>/selecionarEntidade.xhtml</url-pattern>
			<url-pattern>/file/*</url-pattern>
			<http-method>DELETE</http-method>
			<http-method>PUT</http-method>
			<http-method>HEAD</http-method>
			<http-method>OPTIONS</http-method>
			<http-method>TRACE</http-method>
			<http-method>GET</http-method>
			<http-method>POST</http-method>
		</web-resource-collection>
		<auth-constraint>
			<role-name>SICAP_ADMINISTRADOR</role-name>
			<role-name>SICAP_AUDITORES</role-name>
			<role-name>SICAP_INSPETORES</role-name>
		</auth-constraint>
	</security-constraint>
	<security-constraint>
		<web-resource-collection>
			<web-resource-name>Auditoria das Acumulações</web-resource-name>
			<url-pattern>/auditoria/analise/*</url-pattern>
			<url-pattern>/auditoria/distribuicao/*</url-pattern>
			<url-pattern>/auditoria/grupo/*</url-pattern>
			<url-pattern>/relatorios/*</url-pattern>
			<http-method>DELETE</http-method>
			<http-method>PUT</http-method>
			<http-method>HEAD</http-method>
			<http-method>OPTIONS</http-method>
			<http-method>TRACE</http-method>
			<http-method>GET</http-method>
			<http-method>POST</http-method>
		</web-resource-collection>
		<auth-constraint>
			<role-name>SICAP_ADMINISTRADOR</role-name>
		</auth-constraint>
	</security-constraint>
	<context-param>
		<param-name>primefaces.THEME</param-name>
		<param-value>sentinel</param-value>
	</context-param>
	<context-param>
		<param-name>primefaces.FONT_AWESOME</param-name>
		<param-value>true</param-value>
	</context-param>
	<mime-mapping>
		<extension>ttf</extension>
		<mime-type>application/font-sfnt</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>woff</extension>
		<mime-type>application/font-woff</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>woff2</extension>
		<mime-type>application/x-font-woff2</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>eot</extension>
		<mime-type>application/vnd.ms-fontobject</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>eot?#iefix</extension>
		<mime-type>application/vnd.ms-fontobject</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>svg</extension>
		<mime-type>image/svg+xml</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>svg#exosemibold</extension>
		<mime-type>image/svg+xml</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>svg#exobolditalic</extension>
		<mime-type>image/svg+xml</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>svg#exomedium</extension>
		<mime-type>image/svg+xml</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>svg#exoregular</extension>
		<mime-type>image/svg+xml</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>svg#fontawesomeregular</extension>
		<mime-type>image/svg+xml</mime-type>
	</mime-mapping>
	<mime-mapping>
		<extension>jsp</extension>
		<mime-type>text/html</mime-type>
	</mime-mapping>
	<servlet>
		<servlet-name>Faces Servlet</servlet-name>
		<servlet-class>javax.faces.webapp.FacesServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>Faces Servlet</servlet-name>
		<url-pattern>*.xhtml</url-pattern>
	</servlet-mapping>
	<error-page>
		<error-code>403</error-code>
		<location>/403.xhtml?faces-redirect=true</location>
	</error-page>	
</web-app>