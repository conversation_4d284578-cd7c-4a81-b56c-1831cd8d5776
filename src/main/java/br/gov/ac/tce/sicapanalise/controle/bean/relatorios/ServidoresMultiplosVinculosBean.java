package br.gov.ac.tce.sicapanalise.controle.bean.relatorios;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.relatorios.business.ServidoresMultiplosVinculosBusiness;
import br.gov.ac.tce.sicapanalise.relatorios.dto.ServidorMultiplosVinculosDTO;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;

@Named
@ViewScoped
public class ServidoresMultiplosVinculosBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @Inject
    private LoginBean loginBean;
    
    @Inject
    private EntidadeRepositorio entidadeRepositorio;
    
    @Inject
    private RelatorioConexao relatorioConexao;
    
    @Inject
    private ServidoresMultiplosVinculosBusiness servidoresMultiplosVinculosBusiness;

    private Collection<Object[]> listaCompetencias;
    private Collection<Entidade> listaEntidades;
    private Collection<ServidorMultiplosVinculosDTO> listaServidores;

    private String competencia;
    private String cpf;
    private String nome;
    private Integer entidade;
    private Integer minimoVinculos;

    private ServidorMultiplosVinculosDTO servidorSelecionado;

    private int currentLevel;

    public int getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(int currentLevel) {
        this.currentLevel = currentLevel;
    }

    public Collection<Object[]> getListaCompetencias() {
        return listaCompetencias;
    }

    public void setListaCompetencias(Collection<Object[]> listaCompetencias) {
        this.listaCompetencias = listaCompetencias;
    }

    public Collection<Entidade> getListaEntidades() {
        return listaEntidades;
    }

    public void setListaEntidades(Collection<Entidade> listaEntidades) {
        this.listaEntidades = listaEntidades;
    }

    public Collection<ServidorMultiplosVinculosDTO> getListaServidores() {
        return listaServidores;
    }

    public void setListaServidores(Collection<ServidorMultiplosVinculosDTO> listaServidores) {
        this.listaServidores = listaServidores;
    }

    public String getCompetencia() {
        return competencia;
    }

    public void setCompetencia(String competencia) {
        this.competencia = competencia;
    }

    public String getCpf() {
        return cpf;
    }

    public void setCpf(String cpf) {
        this.cpf = cpf;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Integer getEntidade() {
        return entidade;
    }

    public void setEntidade(Integer entidade) {
        this.entidade = entidade;
    }

    public Integer getMinimoVinculos() {
        return minimoVinculos;
    }

    public void setMinimoVinculos(Integer minimoVinculos) {
        this.minimoVinculos = minimoVinculos;
    }

    public ServidorMultiplosVinculosDTO getServidorSelecionado() {
        return servidorSelecionado;
    }

    public void setServidorSelecionado(ServidorMultiplosVinculosDTO servidorSelecionado) {
        this.servidorSelecionado = servidorSelecionado;
    }

    @PostConstruct
    public void init() {
        try {
            this.currentLevel = 1;
            this.entidade = 0;
            this.cpf = "";
            this.nome = "";
            this.minimoVinculos = 3; // Padrão: 3 ou mais vínculos
            this.listaCompetencias = this.servidoresMultiplosVinculosBusiness.listaCompetencias();
            this.listaEntidades = this.entidadeRepositorio.lista();
            
            // Inicializa com pesquisa vazia
            this.listaServidores = this.servidoresMultiplosVinculosBusiness.listaServidoresMultiplosVinculos(
                this.competencia, this.cpf, this.nome, this.entidade, this.minimoVinculos);
        } catch (Exception e) {
            Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível carregar os dados iniciais.");
        }
    }

    public void pesquisar() {
        try {
            this.listaServidores = this.servidoresMultiplosVinculosBusiness.listaServidoresMultiplosVinculos(
                this.competencia, this.cpf, this.nome, this.entidade, this.minimoVinculos);
            this.currentLevel = 1;
        } catch (Exception e) {
            Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível realizar a pesquisa.");
        }
    }

    public void gerarRelatorio() {
        try {
            Map<String, Object> parametros = new HashMap<>();
            parametros.put("COMPETENCIA", this.competencia != null ? this.competencia : "Todas");
            parametros.put("ENTIDADE", this.entidade != null && this.entidade > 0 ? 
                this.listaEntidades.stream()
                    .filter(e -> e.getIdEntidadeCjur().equals(this.entidade))
                    .findFirst()
                    .map(Entidade::getNome)
                    .orElse("Todas") : "Todas");
            parametros.put("CPF", this.cpf != null && !this.cpf.trim().isEmpty() ? this.cpf : "Todos");
            parametros.put("NOME", this.nome != null && !this.nome.trim().isEmpty() ? this.nome : "Todos");
            parametros.put("MINIMO_VINCULOS", this.minimoVinculos);
            parametros.put("USUARIO", this.loginBean.getUsuario().getNome());
            parametros.put("ENTIDADE_USUARIO", this.loginBean.getEntidade().getNome());

            String caminhoRelatorio = FacesContext.getCurrentInstance().getExternalContext()
                .getRealPath("/relatorios/avulsos/templates/servidoresMultiplosVinculos.jasper");

            JasperPrint jasperPrint = JasperFillManager.fillReport(caminhoRelatorio, parametros,
                this.relatorioConexao.getConnection());

            HttpServletResponse response = (HttpServletResponse) FacesContext.getCurrentInstance()
                .getExternalContext().getResponse();

            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=servidores_multiplos_vinculos.pdf");

            JasperExportManager.exportReportToPdfStream(jasperPrint, response.getOutputStream());

            FacesContext.getCurrentInstance().responseComplete();

        } catch (Exception e) {
            Messenger.mostrarMensagem(MessageType.ERRO, "Erro ao gerar relatório: " + e.getMessage());
        }
    }
}
