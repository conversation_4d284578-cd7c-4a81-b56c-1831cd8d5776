package br.gov.ac.tce.sicapanalise.relatorios.business;

import java.util.Collection;

import javax.ejb.Stateless;
import javax.inject.Inject;

import br.gov.ac.tce.sicap.modelo.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.relatorios.dto.ServidorMultiplosVinculosDTO;
import br.gov.ac.tce.sicapanalise.relatorios.repositorio.ServidoresMultiplosVinculosRepositorio;

@Stateless
public class ServidoresMultiplosVinculosBusiness {

    @Inject
    private ServidoresMultiplosVinculosRepositorio servidoresMultiplosVinculosRepositorio;

    public Collection<Object[]> listaCompetencias() throws RepositorioException {
        return servidoresMultiplosVinculosRepositorio.listaCompetencias();
    }

    public Collection<ServidorMultiplosVinculosDTO> listaServidoresMultiplosVinculos(
            String competencia, String cpf, String nome, Integer entidade, Integer minimoVinculos) 
            throws RepositorioException {
        return servidoresMultiplosVinculosRepositorio.listaServidoresMultiplosVinculos(
            competencia, cpf, nome, entidade, minimoVinculos);
    }
}
